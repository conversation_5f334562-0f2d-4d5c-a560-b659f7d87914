const { Currencies } = require("@skywind-group/sw-currency-exchange");

console.log("Currencies with disableGGR: true:");
const disableGGRCurrencies = Currencies.values().filter(({ disableGGR }) => disableGGR);
disableGGRCurrencies.forEach(currency => {
    console.log(`- ${currency.code}: originCurrency=${currency.originCurrency?.currency}, multiplier=${currency.originCurrency?.multiplier}`);
});

console.log("\nAll artificial currencies:");
const artificialCurrencies = Currencies.artificialValues();
artificialCurrencies.forEach(currency => {
    console.log(`- ${currency.code}: originCurrency=${currency.originCurrency?.currency}, multiplier=${currency.originCurrency?.multiplier}, disableGGR=${currency.disableGGR}`);
});
