import config from "./skywind/config";
import { OXRCurrencyProvider } from "./skywind/providers/oxrCurrencyProvider";
import { OANDACurrencyProvider } from "./skywind/providers/oandaCurrencyProvider";
import { SWSCurrencyProvider } from "./skywind/providers/swsCurrencyProvider";
import { LegacyDefaultCurrencyProvider } from "./skywind/providers/legacyDefaultCurrencyProvider";
import { CurrencyExchangeService } from "./skywind/currencyExchangeService";
import { CurrencyExchange, CurrencyProvider, ExchangeRateProvider, ExchangeRateType } from "./skywind/types";
import { RedisPool } from "./skywind/redisTypes";

export * from "./skywind/types";
export * from "./skywind/redisTypes";
export * from "./skywind/currencies";
export * from "./skywind/externalCurrencyReplacement";
export * from "./skywind/gameLimitsCurrencies";

export function getCurrencyProvider(): CurrencyProvider {
    switch (config.provider) {
        case ExchangeRateProvider.OXR:
            return new OXRCurrencyProvider(config.oxr.appKey);
        case ExchangeRateProvider.OANDA:
            return new OANDACurrencyProvider(config.oanda.apiKey);
        case ExchangeRateProvider.SWS:
            return new SWSCurrencyProvider(config.sws.baseUrl, config.sws.internalServerToken);
        default:
            return new LegacyDefaultCurrencyProvider();
    }
}

export async function createCurrencyExchange(pool: RedisPool, type = ExchangeRateType.BID): Promise<CurrencyExchange> {
    const provider = getCurrencyProvider();
    const service = new CurrencyExchangeService();
    await service.init(pool, provider, config, type);
    return service;
}
