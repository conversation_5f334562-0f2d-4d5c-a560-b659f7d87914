import type { CurrencyProvider} from "./provider";
import { Provider } from "./provider";
import type { ProviderExchangeRate } from "../entities/exchangeRates";
import { mapProviderRates } from "./utils";

const currencyResponses = require("./default_currency_rates.json");

export class DefaultCurrencyProvider implements CurrencyProvider {
    private readonly usdRates: ProviderExchangeRate[];

    constructor() {
        const today = new Date();
        this.usdRates = mapProviderRates({
            provider: Provider.DEFAULT,
            ts: today,
            bidRates: {
                [currencyResponses.base]: currencyResponses.rates
            },
            askRates: {
                [currencyResponses.base]: currencyResponses.rates
            }
        });
    }

    public getRates(providerDate: Date, baseCurrencies: string[] = ["USD"]): Promise<ProviderExchangeRate[]> {
        const allRates: ProviderExchangeRate[] = [];

        for (const baseCurrency of baseCurrencies) {
            if (baseCurrency === "USD") {
                // Add USD-based rates directly
                allRates.push(...this.usdRates.map((rate) => ({
                    ...rate,
                    providerDate,
                })));
            } else {
                // Calculate rates for other base currencies using USD rates
                const baseToUsdRate = this.getUsdRate(baseCurrency);
                if (baseToUsdRate) {
                    for (const usdRate of this.usdRates) {
                        if (usdRate.to !== baseCurrency) { // Don't create EUR->EUR rate
                            const convertedRate = usdRate.rate / baseToUsdRate;
                            allRates.push({
                                ...usdRate,
                                from: baseCurrency,
                                rate: convertedRate,
                                providerDate,
                            });
                        }
                    }
                    // Add base currency to USD rate
                    allRates.push({
                        from: baseCurrency,
                        to: "USD",
                        rate: 1 / baseToUsdRate,
                        provider: Provider.DEFAULT,
                        type: this.usdRates[0].type,
                        providerDate,
                    });
                }
            }
        }

        return Promise.resolve(allRates);
    }

    private getUsdRate(currency: string): number | null {
        const usdRate = this.usdRates.find(rate => rate.from === "USD" && rate.to === currency);
        return usdRate ? usdRate.rate : null;
    }
}
